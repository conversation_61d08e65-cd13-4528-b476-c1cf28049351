-- GameFlex Database Initialization Script
-- This script sets up the initial database schema and data for GameFlex

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom schemas
CREATE SCHEMA IF NOT EXISTS gameflex;

-- Set search path
SET search_path TO gameflex, public;

-- Create users table
CREATE TABLE IF NOT EXISTS gameflex.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    avatar_url TEXT,
    bio TEXT,
    date_of_birth DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Create user profiles table for additional user data
CREATE TABLE IF NOT EXISTS gameflex.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone_number VARCHAR(20),
    country VARCHAR(100),
    timezone VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create channels table
CREATE TABLE IF NOT EXISTS gameflex.channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    member_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create channel members table
CREATE TABLE IF NOT EXISTS gameflex.channel_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES gameflex.channels(id) ON DELETE CASCADE,
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member', -- 'owner', 'admin', 'moderator', 'member'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(channel_id, user_id)
);

-- Create posts table
CREATE TABLE IF NOT EXISTS gameflex.posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES gameflex.channels(id) ON DELETE CASCADE,
    content TEXT,
    media_url TEXT,
    media_type VARCHAR(20), -- 'image', 'video', 'gif'
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create comments table
CREATE TABLE IF NOT EXISTS gameflex.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID REFERENCES gameflex.posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    like_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create likes table
CREATE TABLE IF NOT EXISTS gameflex.likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES gameflex.posts(id) ON DELETE CASCADE,
    comment_id UUID REFERENCES gameflex.comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_like_target CHECK (
        (post_id IS NOT NULL AND comment_id IS NULL) OR
        (post_id IS NULL AND comment_id IS NOT NULL)
    ),
    UNIQUE(user_id, post_id),
    UNIQUE(user_id, comment_id)
);

-- Create follows table
CREATE TABLE IF NOT EXISTS gameflex.follows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    follower_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    following_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(follower_id, following_id),
    CONSTRAINT check_no_self_follow CHECK (follower_id != following_id)
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS gameflex.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES gameflex.users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'like', 'comment', 'follow', 'mention', 'channel_invite'
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON gameflex.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON gameflex.users(username);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON gameflex.users(created_at);

CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON gameflex.user_profiles(user_id);

CREATE INDEX IF NOT EXISTS idx_channels_owner_id ON gameflex.channels(owner_id);
CREATE INDEX IF NOT EXISTS idx_channels_is_public ON gameflex.channels(is_public);
CREATE INDEX IF NOT EXISTS idx_channels_created_at ON gameflex.channels(created_at);

CREATE INDEX IF NOT EXISTS idx_channel_members_channel_id ON gameflex.channel_members(channel_id);
CREATE INDEX IF NOT EXISTS idx_channel_members_user_id ON gameflex.channel_members(user_id);

CREATE INDEX IF NOT EXISTS idx_posts_user_id ON gameflex.posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_channel_id ON gameflex.posts(channel_id);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON gameflex.posts(created_at);

CREATE INDEX IF NOT EXISTS idx_comments_post_id ON gameflex.comments(post_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON gameflex.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON gameflex.comments(created_at);

CREATE INDEX IF NOT EXISTS idx_likes_user_id ON gameflex.likes(user_id);
CREATE INDEX IF NOT EXISTS idx_likes_post_id ON gameflex.likes(post_id);
CREATE INDEX IF NOT EXISTS idx_likes_comment_id ON gameflex.likes(comment_id);

CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON gameflex.follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_following_id ON gameflex.follows(following_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON gameflex.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON gameflex.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON gameflex.notifications(created_at);

-- Create functions for updating timestamps
CREATE OR REPLACE FUNCTION gameflex.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updating timestamps
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON gameflex.users
    FOR EACH ROW EXECUTE FUNCTION gameflex.update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON gameflex.user_profiles
    FOR EACH ROW EXECUTE FUNCTION gameflex.update_updated_at_column();

CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON gameflex.channels
    FOR EACH ROW EXECUTE FUNCTION gameflex.update_updated_at_column();

CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON gameflex.posts
    FOR EACH ROW EXECUTE FUNCTION gameflex.update_updated_at_column();

CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON gameflex.comments
    FOR EACH ROW EXECUTE FUNCTION gameflex.update_updated_at_column();

-- Grant permissions
GRANT USAGE ON SCHEMA gameflex TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA gameflex TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA gameflex TO anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA gameflex TO anon, authenticated, service_role;

-- Enable Row Level Security
ALTER TABLE gameflex.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.channel_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE gameflex.notifications ENABLE ROW LEVEL SECURITY;
