import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gameflex_mobile/screens/email_login_screen.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.gfLightTeal,
              AppColors.gfTeal,
              AppColors.gfDarkBlue,
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // GameFlex Logo - Centered in the top half of the screen
                Expanded(
                  flex: 5, // This will take up the top half of the screen
                  child: Center(
                    child: Column(
                      mainAxisAlignment:
                          MainAxisAlignment.center, // Center vertically
                      children: [
                        SizedBox(
                          width:
                              180, // Slightly smaller to balance with wider logo
                          height: 180,
                          child: SvgPicture.asset(
                            'assets/images/icons/icon_teal.svg',
                            fit: BoxFit.contain,
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Logo takes up 80% of screen width (10% margin on each side)
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // Calculate 80% of the available width
                            double logoWidth =
                                MediaQuery.of(context).size.width * 0.8;
                            return SizedBox(
                              width: logoWidth,
                              child: SvgPicture.asset(
                                'assets/images/logos/gameflex_text.svg',
                                fit: BoxFit.fitWidth,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                // This will take up the bottom half of the screen for buttons
                Expanded(
                  flex: 5,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // Sign up with email button
                      _buildLoginButton(
                        icon: Icons.email_outlined,
                        text: 'Sign up with email',
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const EmailLoginScreen(),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      // Apple login button
                      _buildLoginButton(
                        icon: Icons.apple,
                        text: 'Log in with Apple ID',
                        backgroundColor: Colors.white,
                        textColor: Colors.black,
                        onPressed: () {
                          // Apple login functionality
                        },
                      ),
                      const SizedBox(height: 16),
                      // Google login button
                      _buildLoginButton(
                        icon: Icons.g_mobiledata,
                        text: 'Log in with Google',
                        backgroundColor: Colors.white,
                        textColor: Colors.black,
                        onPressed: () {
                          // Google login functionality
                        },
                      ),
                      const SizedBox(height: 24),
                      // Log in text button
                      Center(
                        child: TextButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const EmailLoginScreen(isLogin: true),
                              ),
                            );
                          },
                          child: const Text(
                            'Log in',
                            style: TextStyle(
                              color: AppColors.gfGreen,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton({
    required IconData icon,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      height: 50,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 0,
        ),
        icon: Icon(icon, size: 24),
        label: Text(
          text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: textColor,
          ),
        ),
      ),
    );
  }
}
