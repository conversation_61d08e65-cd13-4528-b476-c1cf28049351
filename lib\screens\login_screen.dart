import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_button.dart';
import 'package:gameflex_mobile/widgets/common/gf_text_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _showEmailForm = false;
  bool _isLogin = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _toggleEmailForm({bool isLogin = false}) {
    setState(() {
      _showEmailForm = !_showEmailForm;
      _isLogin = isLogin;
    });
  }

  void _handleSignIn() {
    if (_formKey.currentState?.validate() ?? false) {
      // In a real app, we would handle authentication here
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.gfLightTeal,
              AppColors.gfTeal,
              AppColors.gfDarkBlue,
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // GameFlex Logo - Centered in the top half of the screen
                Expanded(
                  flex: 5, // This will take up the top half of the screen
                  child: Center(
                    child: Column(
                      mainAxisAlignment:
                          MainAxisAlignment.center, // Center vertically
                      children: [
                        SizedBox(
                          width:
                              180, // Slightly smaller to balance with wider logo
                          height: 180,
                          child: SvgPicture.asset(
                            'assets/images/icons/icon_teal.svg',
                            fit: BoxFit.contain,
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Logo takes up 80% of screen width (10% margin on each side)
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // Calculate 80% of the available width
                            double logoWidth =
                                MediaQuery.of(context).size.width * 0.8;
                            return SizedBox(
                              width: logoWidth,
                              child: SvgPicture.asset(
                                'assets/images/logos/gameflex_text.svg',
                                fit: BoxFit.fitWidth,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                // This will take up the bottom half of the screen for buttons
                Expanded(
                  flex: 5,
                  child: Column(
                    mainAxisAlignment:
                        MainAxisAlignment
                            .center, // Center the buttons vertically in the bottom half
                    children: [
                      const SizedBox(height: 40), // Push buttons down further
                      // Sign up with email button
                      _buildLoginButton(
                        icon: Icons.email_outlined,
                        text: 'Sign up with email',
                        backgroundColor: Colors.black,
                        textColor: Colors.white,
                        onPressed: () {
                          setState(() {
                            _showEmailForm = true;
                            _isLogin = false;
                          });
                        },
                      ),

                      // Email form that appears when button is clicked
                      if (_showEmailForm) ...[
                        const SizedBox(height: 20),
                        Container(
                          width: MediaQuery.of(context).size.width * 0.8,
                          height: 320, // Fixed height for the form
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(
                              alpha: 179,
                            ), // 0.7 opacity
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SingleChildScrollView(
                            child: Form(
                              key: _formKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  GFTextField(
                                    label: 'Email',
                                    hint: 'Enter your email',
                                    controller: _emailController,
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your email';
                                      }
                                      if (!value.contains('@')) {
                                        return 'Please enter a valid email';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                  GFTextField(
                                    label: 'Password',
                                    hint: 'Enter your password',
                                    controller: _passwordController,
                                    obscureText: !_isPasswordVisible,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter your password';
                                      }
                                      if (value.length < 6) {
                                        return 'Password must be at least 6 characters';
                                      }
                                      return null;
                                    },
                                    suffixIcon: IconButton(
                                      icon: Icon(
                                        _isPasswordVisible
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: AppColors.gfGrayText,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 20),
                                  GFButton(
                                    text: _isLogin ? 'Log In' : 'Sign Up',
                                    onPressed: _handleSignIn,
                                    type: GFButtonType.primary,
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        _showEmailForm = false;
                                      });
                                    },
                                    child: const Text(
                                      'Cancel',
                                      style: TextStyle(
                                        color: AppColors.gfGreen,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 20),
                      // Apple login button
                      _buildLoginButton(
                        icon: Icons.apple,
                        text: 'Log in with Apple ID',
                        backgroundColor: Colors.white,
                        textColor: Colors.black,
                        onPressed: () {
                          // Apple login functionality
                        },
                      ),
                      const SizedBox(height: 20),
                      // Google login button
                      _buildLoginButton(
                        icon: Icons.g_mobiledata,
                        text: 'Log in with Google',
                        backgroundColor: Colors.white,
                        textColor: Colors.black,
                        onPressed: () {
                          // Google login functionality
                        },
                      ),
                      const SizedBox(height: 24),
                      // Log in text button
                      Center(
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              _showEmailForm = true;
                              _isLogin = true;
                            });
                          },
                          child: const Text(
                            'Log in',
                            style: TextStyle(
                              color: AppColors.gfGreen,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton({
    required IconData icon,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required VoidCallback onPressed,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate 80% of the available width (10% margin on each side)
        double buttonWidth = MediaQuery.of(context).size.width * 0.8;

        return SizedBox(
          width: buttonWidth,
          height: 55, // Slightly taller buttons
          child: ElevatedButton.icon(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor,
              foregroundColor: textColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            icon: Icon(icon, size: 24),
            label: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
            ),
          ),
        );
      },
    );
  }
}
