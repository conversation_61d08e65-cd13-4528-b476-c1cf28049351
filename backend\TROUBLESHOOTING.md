# GameFlex Backend Troubleshooting Guide

This guide helps resolve common issues when running the GameFlex development backend.

## Database Permission Issues (Windows)

### Problem
PostgreSQL container fails to start with error:
```
FATAL: data directory "/var/lib/postgresql/data" has invalid permissions
DETAIL: Permissions should be u=rwx (0700) or u=rwx,g=rx (0750).
```

### Solution
This issue occurs when using bind mounts on Windows. The setup has been updated to use Docker named volumes instead.

**If you're still experiencing this issue:**

1. **Stop and remove all containers and volumes:**
   ```bash
   docker-compose down -v
   ```

2. **Remove any existing data directory:**
   ```bash
   # Windows (PowerShell)
   Remove-Item -Recurse -Force volumes\db\data -ErrorAction SilentlyContinue
   
   # Linux/macOS
   rm -rf volumes/db/data
   ```

3. **Start fresh:**
   ```bash
   # Windows
   .\start.ps1
   
   # Linux/macOS
   ./start.sh
   ```

### Technical Details
- The issue occurs because Windows file permissions don't map correctly to Unix permissions in Docker
- Named volumes solve this by letting <PERSON><PERSON> manage the data storage internally
- The database data is now stored in a Docker volume called `db_data`

## Port Conflicts

### Problem
Services fail to start with "port already in use" errors.

### Solution
1. **Check what's using the ports:**
   ```bash
   # Windows
   netstat -ano | findstr :54321
   netstat -ano | findstr :54322
   netstat -ano | findstr :54323
   
   # Linux/macOS
   lsof -i :54321
   lsof -i :54322
   lsof -i :54323
   ```

2. **Change ports in `.env` file:**
   ```env
   KONG_HTTP_PORT=55321
   POSTGRES_PORT=55322
   STUDIO_PORT=55323
   ```

3. **Restart the services:**
   ```bash
   docker-compose down
   # Then start again with your script
   ```

## Docker Issues

### Problem
"Docker is not running" or "Docker Compose not found"

### Solution
1. **Install Docker Desktop:**
   - Windows: Download from https://docker.com/products/docker-desktop
   - macOS: Download from https://docker.com/products/docker-desktop
   - Linux: Install docker and docker-compose packages

2. **Start Docker Desktop** and wait for it to fully initialize

3. **Verify installation:**
   ```bash
   docker --version
   docker-compose --version
   ```

## Memory Issues

### Problem
Services crash or fail to start due to insufficient memory.

### Solution
1. **Increase Docker memory allocation:**
   - Open Docker Desktop Settings
   - Go to Resources → Advanced
   - Increase Memory to at least 4GB
   - Apply & Restart

2. **Close other applications** to free up memory

3. **Start services one by one** if needed:
   ```bash
   docker-compose up db -d
   # Wait for db to be ready
   docker-compose up auth rest -d
   # Continue with other services
   ```

## Service Health Check Failures

### Problem
Services start but health checks fail, or services are unreachable.

### Solution
1. **Check service logs:**
   ```bash
   docker-compose logs [service-name]
   # Example:
   docker-compose logs db
   docker-compose logs auth
   docker-compose logs kong
   ```

2. **Wait longer for services to start:**
   - Database can take 1-2 minutes on first run
   - All services together can take 3-5 minutes

3. **Restart problematic services:**
   ```bash
   docker-compose restart [service-name]
   ```

## Network Issues

### Problem
Services can't communicate with each other.

### Solution
1. **Check if all services are on the same network:**
   ```bash
   docker network ls
   docker network inspect supabase_network_gameflex
   ```

2. **Recreate the network:**
   ```bash
   docker-compose down
   docker network prune
   docker-compose up -d
   ```

## Authentication Issues

### Problem
Can't log in with development credentials or API keys don't work.

### Solution
1. **Verify environment variables are loaded:**
   ```bash
   docker-compose exec auth env | grep GOTRUE
   ```

2. **Check JWT secrets match:**
   - Ensure `JWT_SECRET` is the same across all services
   - Verify `ANON_KEY` and `SERVICE_ROLE_KEY` are correct

3. **Reset auth service:**
   ```bash
   docker-compose restart auth
   ```

## Storage Issues

### Problem
File uploads fail or storage service is unreachable.

### Solution
1. **Check storage directory permissions:**
   ```bash
   # Linux/macOS
   ls -la volumes/storage/
   chmod 755 volumes/storage/
   
   # Windows - usually not needed with named volumes
   ```

2. **Restart storage service:**
   ```bash
   docker-compose restart storage
   ```

## Complete Reset

### When All Else Fails
If you're experiencing persistent issues, perform a complete reset:

```bash
# Stop everything
docker-compose down -v

# Remove all GameFlex containers
docker container prune -f

# Remove all unused networks
docker network prune -f

# Remove all unused volumes
docker volume prune -f

# Remove any local data directories
# Windows
Remove-Item -Recurse -Force volumes\db\data -ErrorAction SilentlyContinue

# Linux/macOS
rm -rf volumes/db/data

# Start fresh
# Windows
.\start.ps1

# Linux/macOS
./start.sh
```

## Getting Help

If you're still experiencing issues:

1. **Check the logs:**
   ```bash
   docker-compose logs > gameflex-logs.txt
   ```

2. **Check system resources:**
   ```bash
   docker system df
   docker stats
   ```

3. **Verify your environment:**
   - Docker version: `docker --version`
   - Docker Compose version: `docker-compose --version`
   - Available memory: Check Docker Desktop settings
   - Available disk space: Ensure at least 2GB free

4. **Common log locations:**
   - Docker Desktop logs: Docker Desktop → Troubleshoot → Show logs
   - Container logs: `docker-compose logs [service-name]`

## Prevention Tips

1. **Always use the provided scripts** instead of running `docker-compose` directly
2. **Keep Docker Desktop updated**
3. **Regularly clean up unused containers and volumes:**
   ```bash
   docker system prune -f
   ```
4. **Monitor resource usage** in Docker Desktop
5. **Use named volumes** instead of bind mounts when possible
